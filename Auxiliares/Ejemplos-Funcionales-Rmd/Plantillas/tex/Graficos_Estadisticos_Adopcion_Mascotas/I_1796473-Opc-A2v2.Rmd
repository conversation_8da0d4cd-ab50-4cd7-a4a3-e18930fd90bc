---
output:
  pdf_document: default
  html_document: default
  word_document: default
---
```{r inicio, include=FALSE}
library(exams)
#library(tidyverse)
library(datasets)
library(readxl)
library(data.table)
#library(reticulate)  # Comentado para evitar problemas con Python
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen=999)
#options(tinytex.verbose = TRUE)
knitr::opts_chunk$set(warning = FALSE, message = FALSE)

# Configuración para doble columna (Legal 8.5" x 14")
knitr::opts_chunk$set(
  fig.width = 3.4,
  fig.height = 2.5,
  dpi = 300,
  out.width = "\\columnwidth",
  fig.align = "center"
)
```

```{r DefiniciónDeVariables, message=FALSE, warning=FALSE, results='asis'}

# Vector de mascotas
mascotas <- c('loro', 'perro', 'gato', 'gallina', 'hamster', 'cerdo', 'ternero', 'caballo', 'cabra')

# Agregar 20 mascotas más
mascotas <- c(mascotas, 'conejo', 'tortuga', 'pez', 'canario', 'hurón', 'iguana', 'serpiente', 'ratón', 'araña', 'camaleón', 'pato', 'pavo', 'oveja', 'vaca', 'burro', 'ganso', 'paloma', 'cisne', 'lagarto', 'erizo')

# Shuffle the mascotas vector
mascotas <- sample(mascotas)

# Seleccionar una muestra aleatoria de 3 elementos sin repetición
selmascota <- sample(mascotas, 3)

nombremascota1 <- selmascota[1]
nombremascota2 <- selmascota[2]
nombremascota3 <- selmascota[3]

# Crear secuencia del 60 al 300 de 10 en 10
numeros <- seq(60, 600, 10)

# Eliminamos el número 100 del vector
numeros_sin_100 <- setdiff(numeros, 100)

# Ahora hacemos el muestreo de este nuevo vector
enkuestados <- sample(numeros_sin_100, 1)

# Ensure that enkuestados is a multiple of 10
enkuestados <- enkuestados - (enkuestados %% 10)
```

```{r DefiniciónDeVariables2, message=FALSE, warning=FALSE, results='asis'}
# Generar tres números enteros para los porcentajes. Su suma siempre debe ser igual a 100
generar_vector_unico <- function() {
  # Generate two random integers that are multiples of 10 between 10 and 60
  num1 <- sample(seq(10, 60, 10), 1)
  num2 <- sample(seq(10, 60, 10), 1)
  
  # Ensure that the sum of the two numbers is less than 100
  while (num1 + num2 >= 100) {
    num1 <- sample(seq(10, 60, 10), 1)
    num2 <- sample(seq(10, 60, 10), 1)
  }
  
  # Calculate the third number
  num3 <- 100 - num1 - num2
  
  # Return the vector of numbers
  return(c(num1, num2, num3))
}

# Generar y mostrar el vector de porcentajes
vector_resultado <- generar_vector_unico()

mashor <- max(vector_resultado)

porxentaje1 <- vector_resultado[1]
porxentaje2 <- vector_resultado[2]
porxentaje3 <- vector_resultado[3]
####################################################

maskota1 <- (enkuestados*vector_resultado[1])/100 # Número de personas que adoptan maskota1
maskota2 <- (enkuestados*vector_resultado[2])/100
maskota3 <- (enkuestados*vector_resultado[3])/100

mashiormaskota <- max(maskota1, maskota2, maskota3)
```

```{r GeneracionGraficosTikZ, results='asis', warning=FALSE, message=FALSE}

image01 <- '
\\begin{tikzpicture}
  \\node{
\\begin{tabular}{|l|c|}
\\hline
\\textbf{Animal} & \\textbf{Porcentaje de personas } \\\\ 
     & \\textbf{interesadas en adoptar} \\\\ \\hline
     %s & %s \\\\ \\hline
     %s & %s \\\\ \\hline
     %s & %s \\\\ \\hline
\\end{tabular}
};
\\end{tikzpicture}
'

demas <- sample

maskota1_format <- sprintf("%.0f", maskota1)
maskota2_format <- sprintf("%.0f", maskota2)
maskota3_format <- sprintf("%.0f", maskota3)

image01 <-sprintf(image01, selmascota[1], maskota1_format, selmascota[2], maskota2_format, selmascota[3], maskota3_format)
```


Question
========

El líder de un programa de adopción de mascotas encuestó a `r enkuestados` personas para conocer qué animal les interesaría adoptar. Del total de encuestados, el `r vector_resultado[1]`% adoptaría un `r selmascota[1]`, el `r vector_resultado[2]`% adoptaría un(a) `r selmascota[2]` y el `r vector_resultado[3]`% adoptaría un(a) `r selmascota[3]`. 


¿Cuál de las siguientes representaciones NO muestra correctamente la información recolectada en la encuesta?
\

Answerlist
----------
- 
<br/> 
```{r Tabla01, warning=FALSE,echo = FALSE, results = "asis"}
knitr::opts_chunk$set(warning = FALSE, message = FALSE) 
include_tikz(image01, name = "image01", markup = "markdown",format = typ, library = c("3d", "babel"), packages=c("tikz","xcolor"),width = "8.5cm")
```

- 
<br/> 
```{r BarraVertical_comentado, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide", eval=FALSE}
# Chunk de Python comentado para evitar problemas con reticulate
# ```{python BarraVertical, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide"}
import matplotlib.pyplot as plt
import numpy as np

mascota1py = r.nombremascota1 # De R a Python
mascota2py = r.nombremascota2
mascota3py = r.nombremascota3

porcentaje1py = r.porxentaje1 # De R a Python
porcentaje2py = r.porxentaje2
porcentaje3py = r.porxentaje3

mashorpy = r.mashor

# Datos
animales = [mascota1py, mascota2py, mascota3py]
cantidad = [porcentaje1py, porcentaje2py, porcentaje3py]

# Definir colores en tonos de azul
colores_azules = ['#00B3E6', '#0066CC', '#000099']  

# Crear el gráfico de barras
fig, ax = plt.subplots(figsize=(6, 5))

# Ajustar el ancho de las barras aquí para controlar indirectamente el espacio entre ellas
ancho_barras = 0.6  # Un valor más pequeño de lo normal

# Dibujar las barras con el ancho especificado
bars = ax.bar(animales, cantidad, color=colores_azules, width=ancho_barras)

# Resto del código de personalización
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_linewidth(2)
ax.spines['bottom'].set_linewidth(2)
ax.yaxis.grid(True, linestyle='--', linewidth=0.7, color='darkgray')
plt.xticks(fontweight='bold')
plt.yticks(np.arange(0, mashorpy+1, 10), fontweight='bold')
plt.xlabel(" Animales", fontweight='bold')
plt.ylabel("Porcentaje de personas \ninteresadas en adoptar", fontweight='bold')

#plt.tight_layout()
# plt.show()
# ```
```

```{r BarraVertical_R, echo=FALSE, message=FALSE, warning=FALSE, fig.cap="Gráfico de barras vertical - Interés por adopción", out.width="\\columnwidth"}
# Definir variables para el gráfico (equivalentes a las de Python)
animales <- c(nombremascota1, nombremascota2, nombremascota3)
cantidad <- c(porxentaje1, porxentaje2, porxentaje3)

# Gráfico alternativo usando R base, optimizado para doble columna
barplot(cantidad,
        names.arg = animales,
        main = "Interés en adopción de mascotas",
        xlab = "Tipo de animal",
        ylab = "Cantidad de personas",
        col = c("lightblue", "lightgreen", "orange", "pink"),
        cex.main = 0.9,
        cex.lab = 0.8,
        cex.axis = 0.7,
        cex.names = 0.7)

# Agregar valores encima de las barras
text(x = 1:length(cantidad), y = cantidad + max(cantidad)*0.05,
     labels = cantidad, cex = 0.7, font = 2)
```

- 
<br/> 
```{r BarraHorizontal_comentado, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide", eval=FALSE}
# Chunk de Python comentado para evitar problemas con reticulate
# ```{python BarraHorizontal, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide"}
import matplotlib.pyplot as plt
import numpy as np

mascota1py = r.nombremascota1 # De R a Python
mascota2py = r.nombremascota2
mascota3py = r.nombremascota3

porcentaje1py = r.porxentaje1 # De R a Python
porcentaje2py = r.porxentaje2
porcentaje3py = r.porxentaje3

padrino1py = r.maskota1
padrino2py = r.maskota2
padrino3py = r.maskota3

mashorpy = r.mashor
mashiormaskotapy = r.mashiormaskota
niveles = mashiormaskotapy/10

# Datos
animales = [mascota3py, mascota2py, mascota1py]
cantidad = [padrino3py, padrino2py, padrino1py]

# Definir colores en tonos de azul
colores_azules = ['#00B3E6', '#0066CC', '#000099']  

# Crear el gráfico de barras horizontales
fig, ax = plt.subplots(figsize=(8, 6))

# Ajustar el alto de las barras aquí para controlar indirectamente el espacio entre ellas
alto_barras = 0.6  # Un valor más pequeño de lo normal

# Dibujar las barras horizontales con el alto especificado
bars = ax.barh(animales, cantidad, color=colores_azules, height=alto_barras)

for bar in bars:
    # Obtener la posición y el valor de la barra
    valor = bar.get_width()
    # Posicionar el texto ligeramente debajo de la barra. Ajustar el '-0.05' según sea necesario.
    posicion = bar.get_y() - 0.05
    
    # Añadir texto debajo de la barra, a su extrema derecha
    # 'va' cambia a 'top' para alinear el texto en la parte superior del punto especificado,
    # efectivamente moviéndolo debajo de la barra.
    ax.text(valor, posicion, f'{valor}', va='top', ha='right', color='black', fontweight='bold', fontsize=10)

# Ajustes finales y mostrar el gráfico
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_linewidth(2)
ax.spines['bottom'].set_linewidth(2)
ax.xaxis.grid(True, linestyle='--', linewidth=0.7, color='darkgray')
plt.yticks(fontweight='bold')
plt.xticks(np.arange(0, max(cantidad)+10, niveles), fontweight='bold')  # Ajuste en el rango para xticks basado en la máxima cantidad
plt.ylabel("Animales", fontweight='bold')
plt.xlabel("Cantidad de personas \ninteresadas en adoptar", fontweight='bold')

#plt.tight_layout()
# plt.show()
# ```
```

```{r BarraHorizontal_R, echo=FALSE, message=FALSE, warning=FALSE, fig.cap="Gráfico de barras horizontal - Interés por adopción", out.width="\\columnwidth"}
# Definir variables para el gráfico horizontal (equivalentes a las de Python)
animales <- c(nombremascota3, nombremascota2, nombremascota1)
cantidad <- c(maskota3, maskota2, maskota1)

# Gráfico horizontal alternativo usando R base, optimizado para doble columna
par(mar = c(4, 6, 3, 2))  # Ajustar márgenes para etiquetas largas
barplot(cantidad,
        names.arg = animales,
        main = "Interés en adopción de mascotas",
        xlab = "Cantidad de personas",
        ylab = "",
        col = c("lightblue", "lightgreen", "orange", "pink"),
        horiz = TRUE,
        cex.main = 0.9,
        cex.lab = 0.8,
        cex.axis = 0.7,
        cex.names = 0.7,
        las = 1)

# Agregar valores al final de las barras
text(x = cantidad + max(cantidad)*0.05, y = 1:length(cantidad),
     labels = cantidad, cex = 0.7, font = 2)
```

- 
<br/> 
```{r Torta_comentado, echo = FALSE, results = "hide", eval=FALSE}
# Chunk de Python comentado para evitar problemas con reticulate
# ```{python Torta, echo = FALSE, results = "hide"}
# Re-importing necessary libraries after execution state reset
from matplotlib import pyplot as plt

adoptantes1py = r.maskota1 # De R a Python
adoptantes2py = r.maskota2
adoptantes3py = r.maskota3

# Re-defining data for the pie chart
labels = ['Personas \ninteresadas en \nadoptar un \n{}'.format(mascota1py),
          'Personas \ninteresadas en \nadoptar un \n{}'.format(mascota2py),
          'Personas \ninteresadas en \nadoptar un \n{}'.format(mascota3py)]
sizes = [adoptantes1py, adoptantes2py, adoptantes3py]  # Actual values for calculations
sizes2 = [adoptantes1py, adoptantes2py, adoptantes3py]  # Values to display on the labels
colors = ['#1f77b4', '#aec7e8', '#ff7f0e']
explode = (0, 0, 0)  # No slice is exploded

# Labels with the sizes values
pie_labels = ['{}: {}'.format(label, int(size)) for label, size in zip(labels, sizes2)]

# Plot
fig, ax = plt.subplots(figsize=(6, 4), tight_layout=True)  # Adjust the figure size and layout
ax.pie(sizes, explode=explode, labels=pie_labels, colors=colors, shadow=True, startangle=90)
ax.axis('equal')  # Equal aspect ratio ensures that pie chart is drawn as a circle.

# Reducing left and top margins
plt.subplots_adjust(left=0.1, top=0.9)

# Display the plot
# plt.show()
# ```
```

```{r Torta_R, echo=FALSE, message=FALSE, warning=FALSE, fig.cap="Gráfico de torta - Distribución de interés por adopción", out.width="\\columnwidth"}
# Definir variables para el gráfico de torta (equivalentes a las de Python)
adoptantes <- c(maskota1, maskota2, maskota3)
etiquetas <- c(paste("Personas interesadas\nen adoptar un", nombremascota1),
               paste("Personas interesadas\nen adoptar un", nombremascota2),
               paste("Personas interesadas\nen adoptar un", nombremascota3))

# Crear etiquetas con valores
etiquetas_con_valores <- paste(etiquetas, ":", adoptantes)

# Colores similares a los de Python
colores <- c("#1f77b4", "#aec7e8", "#ff7f0e")

# Crear gráfico de torta optimizado para doble columna
pie(adoptantes,
    labels = etiquetas_con_valores,
    col = colores,
    main = "Distribución de interés por adopción",
    cex = 0.7,
    cex.main = 0.9)
```

Solution
========

La gráfica que representa la opción correcta es

<br/> 
```{r Tabla01Sol, warning=FALSE,echo = FALSE, results = "asis"}
knitr::opts_chunk$set(warning = FALSE, message = FALSE) 
include_tikz(image01, name = "image01", markup = "markdown",format = typ, library = c("3d", "babel"), packages=c("tikz","xcolor"),width = "8.5cm")
```

Meta-information
================

exname: I_1796473/2023-Cuadernillo-Matematicas-11-2(single-choice)
extype: schoice
exsolution: 1000
exshuffle: TRUE
